import React, { useState, useRef, useEffect } from "react";
import "./index.css";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowBigRightLinesFilled,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbStar,
  TbSchool,
  TbRocket,
  TbUserPlus,
  TbLogin
} from "react-icons/tb";
import { message } from "antd";
import { useSelector } from "react-redux";
import Image1 from "../../../assets/collage-1.png";
import { contactUs } from "../../../apicalls/users";
import NotificationBell from "../../../components/common/NotificationBell";
import ProfilePicture from "../../../components/common/ProfilePicture";
import TryForFreeModal from "../../../components/common/TryForFreeModal";

const Home = () => {
  const homeSectionRef = useRef(null);
  const reviewsSectionRef = useRef(null);
  const contactUsRef = useRef(null);
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();

  // Handle Try for Free modal
  const handleTryForFree = () => {
    setShowTryForFreeModal(true);
  };

  const handleTryForFreeSubmit = (trialData) => {
    // Navigate to trial experience with user data
    navigate('/trial', { state: { trialUserInfo: trialData } });
    setShowTryForFreeModal(false);
  };



  const scrollToSection = (ref, offset = 80) => {
    if (ref?.current) {
      const sectionTop = ref.current.offsetTop;
      window.scrollTo({ top: sectionTop - offset, behavior: "smooth" });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponseMessage("");
    try {
      const data = await contactUs(formData);
      if (data.success) {
        message.success("Message sent successfully!");
        setResponseMessage("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" });
      } else {
        setResponseMessage(data.message || "Something went wrong.");
      }
    } catch (error) {
      setResponseMessage("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  return (
    <div className="Home relative min-h-screen overflow-hidden">
      {/* Modern Responsive Header - Same as ProtectedRoute */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20"
      >
        <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
          <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
            {/* Left section - Reviews */}
            <div className="flex items-center space-x-2">
              <div className="hidden md:flex items-center space-x-4 lg:space-x-6">
                <button onClick={() => scrollToSection(reviewsSectionRef)} className="nav-item text-sm md:text-base">Reviews</button>
              </div>
            </div>

            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}
            <div className="flex-1 flex justify-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative group flex items-center space-x-3"
              >
                {/* Tanzania Flag - Using actual flag image */}
                <div
                  className="rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative"
                  style={{
                    width: '32px',
                    height: '24px'
                  }}
                >
                  <img
                    src="https://flagcdn.com/w40/tz.png"
                    alt="Tanzania Flag"
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      // Fallback to another flag source if first fails
                      e.target.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png";
                      e.target.onerror = () => {
                        // Final fallback - hide image and show text
                        e.target.style.display = 'none';
                        e.target.parentElement.innerHTML = '<div class="w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold">TZ</div>';
                      };
                    }}
                  />
                </div>

                {/* Amazing Animated Brainwave Text */}
                <div className="relative brainwave-container">
                  <h1 className="text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none"
                      style={{
                        fontFamily: "'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif",
                        letterSpacing: '-0.02em'
                      }}>
                    {/* Brain - with amazing effects */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: -30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        textShadow: [
                          "0 0 10px rgba(59, 130, 246, 0.5)",
                          "0 0 20px rgba(59, 130, 246, 0.8)",
                          "0 0 10px rgba(59, 130, 246, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.3,
                        textShadow: {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, -2, 2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#1f2937',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
                      }}
                    >
                      Brain

                      {/* Electric spark */}
                      <motion.div
                        className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          scale: [0.5, 1.2, 0.5],
                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          delay: 2
                        }}
                        style={{
                          backgroundColor: '#3b82f6',
                          boxShadow: '0 0 10px #3b82f6'
                        }}
                      />
                    </motion.span>

                    {/* Wave - with flowing effects (no space) */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: 30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        y: [0, -2, 0, 2, 0],
                        textShadow: [
                          "0 0 10px rgba(16, 185, 129, 0.5)",
                          "0 0 20px rgba(16, 185, 129, 0.8)",
                          "0 0 10px rgba(16, 185, 129, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.5,
                        y: {
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        },
                        textShadow: {
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, 2, -2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#059669',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
                      }}
                    >
                      wave

                      {/* Wave particle */}
                      <motion.div
                        className="absolute top-0 left-0 w-1.5 h-1.5 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          x: [0, 40, 80],
                          y: [0, -5, 0, 5, 0],
                          backgroundColor: ['#10b981', '#34d399', '#10b981']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: 1
                        }}
                        style={{
                          backgroundColor: '#10b981',
                          boxShadow: '0 0 8px #10b981'
                        }}
                      />
                    </motion.span>
                  </h1>

                  {/* Glowing underline effect */}
                  <motion.div
                    className="absolute -bottom-1 left-0 h-1 rounded-full"
                    initial={{ width: 0, opacity: 0 }}
                    animate={{
                      width: '100%',
                      opacity: 1,
                      boxShadow: [
                        '0 0 10px rgba(16, 185, 129, 0.5)',
                        '0 0 20px rgba(59, 130, 246, 0.8)',
                        '0 0 10px rgba(16, 185, 129, 0.5)'
                      ]
                    }}
                    transition={{
                      duration: 1.5,
                      delay: 1.2,
                      boxShadow: {
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                    style={{
                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',
                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                    }}
                  />
                </div>

                {/* Official Logo - Small like profile */}
                <div
                  className="rounded-full overflow-hidden border-2 border-white/20 relative"
                  style={{
                    background: '#f0f0f0',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                    width: '32px',
                    height: '32px'
                  }}
                >
                  <img
                    src="/favicon.png"
                    alt="Brainwave Logo"
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                  <div
                    className="w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold"
                    style={{
                      display: 'none',
                      fontSize: '12px'
                    }}
                  >
                    🧠
                  </div>
                </div>

                {/* Modern Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
              </motion.div>
            </div>

            {/* Right Section - Contact Us + Notifications + User Profile */}
            <div className="flex items-center justify-end space-x-2 sm:space-x-3">
              {/* Contact Us Button */}
              <div className="hidden md:flex items-center space-x-4 lg:space-x-6">
                <button onClick={() => scrollToSection(contactUsRef)} className="nav-item text-sm md:text-base">Contact Us</button>
              </div>

              {/* Notification Bell */}
              {user && !user?.isAdmin && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <NotificationBell />
                </motion.div>
              )}

              {/* User Profile Section */}
              {user && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-center space-x-2 group"
                >
                  {/* Profile Picture with Online Status */}
                  <ProfilePicture
                    user={user}
                    size="sm"
                    showOnlineStatus={true}
                    style={{
                      width: '32px',
                      height: '32px'
                    }}
                  />

                  {/* User Name and Class */}
                  <div className="hidden sm:block text-right">
                    <div className="text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300">
                      Class {user?.class}
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </motion.header>

      {/* FULL PAGE GALAXY ANIMATION */}
      {/* PROFESSIONAL HERO SECTION */}
      <section className="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">


            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              {/* Premium Badge */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <TbStar className="w-5 h-5 mr-2 animate-pulse" />
                <span className="tracking-wide">#1 Premium Educational Platform in Tanzania</span>
              </motion.div>

              {/* Premium Main Heading */}
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-extrabold text-gray-900 leading-tight tracking-tight"
              >
                <div className="mb-2">Transform Your Future</div>
                <div className="mb-2">with{" "}
                  <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                    Elite Education
                  </span>
                </div>
              </motion.h1>

              {/* Premium Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="space-y-4"
              >
                <p className="text-lg sm:text-xl text-gray-700 leading-relaxed font-medium max-w-2xl">
                  Experience world-class education with our cutting-edge AI-powered learning platform.
                  Join thousands of successful students who've unlocked their potential.
                </p>

                <div className="flex flex-wrap gap-2 text-base font-semibold">
                  <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full">Premium Study Solutions</span>
                  <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full">Personalized Learning Paths</span>
                </div>
              </motion.div>

              {/* Premium Features Highlight */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.45 }}
                className="grid grid-cols-1 sm:grid-cols-3 gap-4"
              >
                <div className="flex items-center justify-center sm:justify-start space-x-2 p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                  <div className="p-2 bg-green-500 rounded-full">
                    <TbBrain className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-green-700 font-semibold text-sm">AI-Powered Learning</span>
                </div>

                <div className="flex items-center justify-center sm:justify-start space-x-2 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                  <div className="p-2 bg-blue-500 rounded-full">
                    <TbTrophy className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-blue-700 font-semibold text-sm">98% Success Rate</span>
                </div>

                <div className="flex items-center justify-center sm:justify-start space-x-2 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                  <div className="p-2 bg-purple-500 rounded-full">
                    <TbStar className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-purple-700 font-semibold text-sm">Expert-Crafted Content</span>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                {!user ? (
                  <>
                    <motion.button
                      onClick={handleTryForFree}
                      className="group px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="flex items-center justify-center space-x-2">
                        <TbRocket className="w-5 h-5 group-hover:animate-bounce" />
                        <span>Try for Free</span>
                      </div>
                    </motion.button>

                    <div className="flex flex-col sm:flex-row gap-4">
                      <Link to="/register">
                        <motion.button
                          className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <div className="flex items-center justify-center space-x-2">
                            <TbUserPlus className="w-5 h-5" />
                            <span>Register Now</span>
                          </div>
                        </motion.button>
                      </Link>

                      <Link to="/login">
                        <motion.button
                          className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <div className="flex items-center justify-center space-x-2">
                            <TbLogin className="w-5 h-5" />
                            <span>Login Now</span>
                          </div>
                        </motion.button>
                      </Link>
                    </div>
                  </>
                ) : (
                  <Link to="/dashboard">
                    <motion.button
                      className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="flex items-center justify-center space-x-2">
                        <TbArrowBigRightLinesFilled className="w-5 h-5" />
                        <span>Go to Dashboard</span>
                      </div>
                    </motion.button>
                  </Link>
                )}
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="grid grid-cols-3 gap-8 pt-8"
              >
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">10K+</div>
                  <div className="text-sm text-gray-600">Students</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">500+</div>
                  <div className="text-sm text-gray-600">Courses</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">95%</div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Content - Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative">
                {/* Main Image */}
                <div className="relative z-10 bg-white rounded-3xl shadow-2xl p-8">
                  <img
                    src={Image1}
                    alt="Students Learning"
                    className="w-full h-auto rounded-2xl"
                    loading="lazy"
                  />
                </div>

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -top-6 -right-6 bg-blue-500 text-white p-4 rounded-2xl shadow-lg z-20"
                >
                  <TbTrophy className="w-8 h-8" />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                  className="absolute -bottom-6 -left-6 bg-green-500 text-white p-4 rounded-2xl shadow-lg z-20"
                >
                  <TbBook className="w-8 h-8" />
                </motion.div>

                <motion.div
                  animate={{ y: [-5, 5, -5] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
                  className="absolute top-1/2 -left-8 bg-purple-500 text-white p-3 rounded-xl shadow-lg z-20"
                >
                  <TbBrain className="w-6 h-6" />
                </motion.div>

                {/* Background Decoration */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl transform rotate-6 scale-105"></div>
              </div>
            </motion.div>

          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8"
          >
            {[
              { number: "15K+", text: "Active Students", icon: TbUsers, color: "from-blue-500 to-blue-600" },
              { number: "500+", text: "Expert Teachers", icon: TbSchool, color: "from-green-500 to-green-600" },
              { number: "1000+", text: "Video Lessons", icon: TbBook, color: "from-purple-500 to-purple-600" },
              { number: "98%", text: "Success Rate", icon: TbTrophy, color: "from-orange-500 to-orange-600" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100"
              >
                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                </div>
                <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2">{stat.number}</div>
                <div className="text-xs sm:text-sm md:text-base text-gray-600 font-medium">{stat.text}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Reviews Section */}
      <section ref={reviewsSectionRef} className="reviews-section">
        <div className="reviews-container">
          <h2 className="reviews-title">
            Reviews from our students
          </h2>
          <div className="reviews-grid">
            {[
              {
                rating: 5,
                text: "BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.",
                user: { name: "Sarah Johnson" }
              },
              {
                rating: 5,
                text: "The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.",
                user: { name: "Michael Chen" }
              },
              {
                rating: 5,
                text: "Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!",
                user: { name: "Amina Hassan" }
              }
            ].map((review, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="review-card"
              >
                <div className="review-rating">
                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>
                    {'★'.repeat(review.rating)}
                  </div>
                </div>
                <div className="review-text">"{review.text}"</div>
                <div className="review-divider"></div>
                <div className="review-author">{review.user?.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section ref={contactUsRef} className="py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4"
            >
              Contact Us
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-lg text-gray-600 max-w-2xl mx-auto"
            >
              Get in touch with us for any questions or support. We're here to help you succeed!
            </motion.p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl shadow-xl p-6 sm:p-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-gray-800">Send us a Message</h3>
                <div className="text-sm text-gray-500">or</div>
                <a
                  href="https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg font-bold hover:bg-green-700 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl border-2 border-green-500 hover:border-green-600"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346"/>
                  </svg>
                  <span className="hidden sm:inline">WhatsApp</span>
                </a>
              </div>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  <input
                    type="text"
                    name="name"
                    placeholder="Your Full Name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    name="email"
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                  <textarea
                    name="message"
                    placeholder="Tell us how we can help you..."
                    rows="5"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    value={formData.message}
                    onChange={handleChange}
                    required
                  ></textarea>
                </div>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? "Sending..." : "Send Message"}
                </button>
                {responseMessage && (
                  <p className="text-center text-green-600 font-medium">
                    {responseMessage}
                  </p>
                )}
              </form>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              {/* Contact Methods */}
              <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-6">Other Ways to Reach Us</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346"/>
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">WhatsApp Support</p>
                      <p className="text-gray-600">+255 655 285 49</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Email Support</p>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Response Time</p>
                      <p className="text-gray-600">Usually within 2 hours</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">Location</p>
                      <p className="text-gray-600">Dar es Salaam, Tanzania</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <p className="footer-text">
            © 2024 BrainWave Educational Platform. All rights reserved.
          </p>
        </div>
      </footer>

      {/* Try for Free Modal */}
      <TryForFreeModal
        key={showTryForFreeModal ? 'open' : 'closed'}
        isOpen={showTryForFreeModal}
        onClose={() => setShowTryForFreeModal(false)}
        onSubmit={handleTryForFreeSubmit}
      />
    </div>
  );
};

export default Home;
